import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
import requests
from threading import Thread

class OpenRouterChatApp:
    def __init__(self, root):
        self.root = root
        self.root.title("OpenRouter Chat - GLM-4.5-AIR")
        self.root.geometry("800x600")
        
        # Configuration
        self.config_file = "openrouter_config.json"
        self.api_key = None
        self.thinking_mode = False
        self.conversation_history = []
        
        # Load configuration
        self.load_config()
        
        # Create GUI
        self.create_menu()
        self.create_chat_interface()
        
    def load_config(self):
        """Load configuration from file if it exists"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.api_key = config.get('api_key', '')
                    self.thinking_mode = config.get('thinking_mode', False)
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load config: {e}")
    
    def save_config(self):
        """Save configuration to file"""
        try:
            config = {
                'api_key': self.api_key,
                'thinking_mode': self.thinking_mode
            }
            with open(self.config_file, 'w') as f:
                json.dump(config, f)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save config: {e}")
    
    def create_menu(self):
        """Create the menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(label="API Key", command=self.configure_api_key)
        settings_menu.add_command(label="Thinking Mode", command=self.toggle_thinking_mode)
        settings_menu.add_separator()
        settings_menu.add_command(label="Save Settings", command=self.save_config)
    
    def configure_api_key(self):
        """Configure API key through dialog"""
        api_key = simpledialog.askstring(
            "API Key Configuration",
            "Enter your OpenRouter API key:",
            initialvalue=self.api_key or "",
            parent=self.root
        )
        if api_key is not None:  # User didn't cancel
            self.api_key = api_key.strip()
            if self.api_key:
                messagebox.showinfo("Success", "API key saved successfully!")
            else:
                messagebox.showwarning("Warning", "API key cleared.")
    
    def toggle_thinking_mode(self):
        """Toggle thinking mode"""
        self.thinking_mode = not self.thinking_mode
        status = "enabled" if self.thinking_mode else "disabled"
        messagebox.showinfo("Thinking Mode", f"Thinking mode {status}")
    
    def create_chat_interface(self):
        """Create the main chat interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Status bar
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="Ready")
        self.status_label.pack(side=tk.LEFT)
        
        thinking_status = ttk.Label(
            status_frame, 
            text=f"Thinking Mode: {'ON' if self.thinking_mode else 'OFF'}"
        )
        thinking_status.pack(side=tk.RIGHT)
        
        # Chat display area
        chat_frame = ttk.Frame(main_frame)
        chat_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.chat_text = tk.Text(chat_frame, wrap=tk.WORD, state=tk.DISABLED)
        chat_scrollbar = ttk.Scrollbar(chat_frame, orient=tk.VERTICAL, command=self.chat_text.yview)
        self.chat_text.configure(yscrollcommand=chat_scrollbar.set)
        
        self.chat_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        chat_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        chat_frame.columnconfigure(0, weight=1)
        chat_frame.rowconfigure(0, weight=1)
        
        # Input area
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        self.input_field = ttk.Entry(input_frame)
        self.input_field.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        self.input_field.bind('<Return>', lambda e: self.send_message())
        
        send_button = ttk.Button(input_frame, text="Send", command=self.send_message)
        send_button.grid(row=0, column=1)
        
        input_frame.columnconfigure(0, weight=1)
    
    def send_message(self):
        """Send message to OpenRouter API"""
        message = self.input_field.get().strip()
        if not message:
            return
        
        if not self.api_key:
            messagebox.showerror("Error", "Please configure your API key first!")
            return
        
        # Clear input field
        self.input_field.delete(0, tk.END)
        
        # Add user message to chat
        self.add_message("You", message)
        
        # Update status
        self.status_label.config(text="Sending to OpenRouter...")
        
        # Send to OpenRouter in a separate thread
        Thread(target=self.call_openrouter_api, args=(message,)).start()
    
    def call_openrouter_api(self, message):
        """Call OpenRouter API with the message"""
        try:
            url = "https://openrouter.ai/api/v1/chat/completions"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "http://localhost",
                "X-Title": "OpenRouter Chat App"
            }
            
            # Prepare messages with conversation history
            messages = self.conversation_history.copy()
            messages.append({"role": "user", "content": message})
            
            data = {
                "model": "z-ai/glm-4.5-air:free",
                "messages": messages,
                "reasoning": self.thinking_mode
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            assistant_message = result['choices'][0]['message']['content']
            
            # Update UI in main thread
            self.root.after(0, lambda: self.handle_api_response(assistant_message, message))
            
        except Exception as e:
            self.root.after(0, lambda error=e: self.handle_api_error(error))
    
    def handle_api_response(self, assistant_message, user_message):
        """Handle successful API response"""
        # Add to conversation history
        self.conversation_history.append({"role": "user", "content": user_message})
        self.conversation_history.append({"role": "assistant", "content": assistant_message})
        
        # Add assistant message to chat
        self.add_message("Assistant", assistant_message)
        
        # Update status
        self.status_label.config(text="Ready")
    
    def handle_api_error(self, error):
        """Handle API error"""
        try:
            self.status_label.config(text="Error")
            messagebox.showerror("API Error", f"Failed to get response: {error}")
        except Exception:
            # Window might be closed, ignore the error
            pass
    
    def add_message(self, sender, message):
        """Add a message to the chat display"""
        self.chat_text.config(state=tk.NORMAL)
        self.chat_text.insert(tk.END, f"{sender}: {message}\n\n")
        self.chat_text.see(tk.END)
        self.chat_text.config(state=tk.DISABLED)

def main():
    root = tk.Tk()
    app = OpenRouterChatApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
