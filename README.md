# OpenRouter Chat GUI

A simple Python GUI application for chatting with the GLM-4.5-AIR model through OpenRouter API.

## Features

- **Chat Interface**: Clean GUI for sending messages and viewing responses
- **API Key Management**: Configure your OpenRouter API key through the GUI
- **Thinking Mode Toggle**: Enable/disable the GLM-4.5-AIR model's reasoning capability
- **Settings Persistence**: API key and settings are saved locally
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Prerequisites

- Python 3.6+
- OpenRouter account with API key
- Internet connection

## Installation

1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install requests
   ```

## Getting an OpenRouter API Key

1. Go to [OpenRouter website](https://openrouter.ai/)
2. Sign up for a free account
3. Navigate to your account settings
4. Generate an API key
5. Note: The GLM-4.5-AIR:free model may require credits for usage

## Usage

1. Run the application:
   ```bash
   python3 openrouter_chat.py
   ```

2. Configure your API key:
   - Click on "Settings" in the menu bar
   - Select "API Key"
   - Enter your OpenRouter API key
   - Click "OK"

3. (Optional) Toggle thinking mode:
   - Go to Settings → Thinking Mode
   - This enables/disables the model's advanced reasoning capability

4. Start chatting:
   - Type your message in the input field
   - Press Enter or click "Send"
   - Responses will appear in the chat display area

## File Structure

- `openrouter_chat.py` - Main application file
- `openrouter_config.json` - Configuration file (created automatically)

## Troubleshooting

### Common Issues

1. **API Key Error**: Make sure you've entered a valid OpenRouter API key
2. **Network Issues**: Check your internet connection
3. **Credit Limit**: Ensure you have sufficient credits in your OpenRouter account

### Error Messages

- "Please configure your API key first!" - You need to set up your API key before chatting
- "Failed to get response" - There was an issue with the API request

## Model Information

This application uses the `z-ai/glm-4.5-air:free` model through OpenRouter. This model supports:

- **Thinking Mode**: Advanced reasoning and tool use capabilities
- **Non-Thinking Mode**: Real-time interaction mode
- **Free Tier**: Available with OpenRouter credits

## License

This project is open source and available under the MIT License.
